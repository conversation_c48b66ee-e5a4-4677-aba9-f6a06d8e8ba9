function processOrderData() {
  // Получаем активный лист
  var sheet = SpreadsheetApp.getActiveSheet();
  
  // Получаем данные из ячейки A5
  var rawData = sheet.getRange("A5").getValue();
  
  // Проверяем, что данные не пустые
  if (!rawData || rawData.toString().trim() === "") {
    Logger.log("Ячейка A5 пуста");
    return;
  }
  
  // Преобразуем в строку и разбиваем на части
  var dataString = rawData.toString();
  
  // Регулярное выражение для поиска номеров заказов и цен
  // Ищем паттерн: S + 8-10 символов + возможно "check" или "zero" + пробел + (цена)
  var pattern = /(S[A-F0-9]{8,10}(?:check|zero)?)\s*\(([0-9]+\.?[0-9]*)\)/gi;
  
  var matches = [];
  var match;
  
  // Находим все совпадения
  while ((match = pattern.exec(dataString)) !== null) {
    var orderNumber = match[1];
    var price = match[2];
    
    // Удаляем "check" и "zero" из номера заказа
    orderNumber = orderNumber.replace(/check|zero/gi, '');
    
    // Заменяем точку на запятую в цене
    price = price.replace('.', ',');
    
    matches.push([orderNumber, price]);
  }
  
  // Очищаем область начиная с A5
  if (matches.length > 0) {
    // Очищаем старые данные (предполагаем максимум 100 строк)
    var clearRange = sheet.getRange(5, 1, 100, 2);
    clearRange.clear();
    
    // Записываем новые данные
    var outputRange = sheet.getRange(5, 1, matches.length, 2);
    outputRange.setValues(matches);
    
    Logger.log("Обработано " + matches.length + " заказов");
  } else {
    Logger.log("Не найдено ни одного заказа в указанном формате");
  }
}

// Функция для тестирования с вашими данными
function testWithSampleData() {
  var sheet = SpreadsheetApp.getActiveSheet();
  
  // Вставляем ваши тестовые данные в A5
  var sampleData = "S251D55799 (16.49) S254915530 (6.74) S25ADD0E95 (15.00) S25979D003 (16.49) S2519EFA65 (16.49) S25DFBB600 (17.81) S254D26A1Czero (16.79) S253149612 (14.81) S25D4CECDE (16.49) S254CF6264 (15.39) S253C505D5 (7.50) S25C276B2A (15.39) S25A869D10 (21.74) S251CE408B (15.39) S254D5D014 (7.19) S257B0ADDA (6.74) S25C42C214 (15.94) S2551E3FAA (15.39) S259CE5CE5 (5.39) S2515672E9 (6.74) S25286EFCA (15.94) S257458312 (16.49) S2587ADA59 (16.49) S25105EF4C (15.39) S25AF738B3 (15.39) S25AF564A9 (15.39) S250F7F16C (15.39) S25B6C850C (15.39) S25A3024F6 (15.39) S25EB33D85 (17.99) S251301DCE (13.19) S2555FABD7 (17.99) S25FEFA027 (13.19) S258594F95 (17.99) S25B5F42B0 (17.99) S2591510B9 (240.00) S2539F69C0 (12.00) S256654543zero (12.00) S2514443DB (12.64) S254B83606 (12.64) S259C7BBFC (12.00) S25ABE9191 (13.19) S25D9AA6A0 (17.99) S25B6785B5 (4.94) S25D613732 (12.00) S259025508 (13.19) S258E15DA0 (13.19) S255F85A28 (12.37) S258ED4842 (12.64) S2563619E5check (15.17) S25164AD63 (12.64) S25E72CBDC (17.24) S2559A6459 (7.50) S250205AD2 (12.64) S25586C796 (17.99) S2505E3142 (11.54) S25DA7B56B (11.54) S254FA2872check (5.40) S2503AE16Ccheck (18.59)";
  
  sheet.getRange("A5").setValue(sampleData);
  
  // Запускаем обработку
  processOrderData();
}

// Функция для создания меню в Google Sheets
function onOpen() {
  var ui = SpreadsheetApp.getUi();
  ui.createMenu('Обработка заказов')
    .addItem('Обработать данные из A5', 'processOrderData')
    .addItem('Тест с примером данных', 'testWithSampleData')
    .addToUi();
}
