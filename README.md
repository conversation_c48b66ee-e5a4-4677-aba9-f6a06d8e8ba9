# Обработка данных заказов в Google Sheets

## Описание
Этот Google Apps Script обрабатывает строку с номерами заказов и ценами, извлекает номера заказов и цены, очищает номера от слов "check" и "zero", заменяет точки на запятые в ценах.

## Установка

1. Откройте Google Sheets
2. Перейдите в меню "Расширения" → "Apps Script"
3. Удалите код по умолчанию и вставьте код из файла `processOrders.gs`
4. Сохраните проект (Ctrl+S)
5. Вернитесь в Google Sheets

## Использование

### Автоматический способ:
1. Вставьте ваши данные в ячейку A5
2. В меню появится "Обработка заказов"
3. Выберите "Обработать данные из A5"

### Ручной способ:
1. Вставьте данные в ячейку A5
2. В Apps Script запустите функцию `processOrderData()`

### Тестирование:
- Используйте функцию `testWithSampleData()` для тестирования с вашими примерными данными

## Результат

Скрипт создаст таблицу начиная с ячейки A5:
- Столбец A: Номера заказов (без "check" и "zero")
- Столбец B: Цены (с запятыми вместо точек)

## Пример входных данных:
```
S251D55799 (16.49) S254915530 (6.74) S254D26A1Czero (16.79) S2563619E5check (15.17)
```

## Пример результата:
```
A5: S251D55799    B5: 16,49
A6: S254915530    B6: 6,74
A7: S254D26A1C    B7: 16,79
A8: S2563619E5    B8: 15,17
```

## Особенности:
- Автоматически удаляет слова "check" и "zero" из номеров заказов
- Заменяет точки на запятые в ценах
- Очищает предыдущие результаты перед записью новых
- Поддерживает номера заказов длиной 8-10 символов после "S"
